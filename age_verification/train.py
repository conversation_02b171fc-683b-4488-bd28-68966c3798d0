import os
import sys
import tqdm
import torch
from torch.nn import CrossEntropyLoss
from torch.optim import AdamW
from sklearn.metrics import accuracy_score, f1_score  # Import for metrics

from age_verification.data.dataset import FaceCropDataset  # Import FaceCropDataset
from age_verification.data.dataloader import create_face_dataloaders
from age_verification.model.model_defination import load_gemma3n_model_for_classification, load_gemma3n_processor
from gemma3_local.src.transformers import get_scheduler

gemma_local_path = "/home/<USER>/Documents/Anas/age_verification_gemma3n/gemma3_local/src"
sys.path.insert(0, gemma_local_path)

# --- Configuration ---
# Ideally, these would come from a separate config.py file
model_name = "/home/<USER>/Documents/Anas/age_verification_gemma3n/models/pretrained_gemma3n/gemma-3n-E2B"
num_labels = 2
num_epochs = 3
batch_size = 2
learning_rate = 5e-5
gradient_accumulation_steps = 4
log_interval = 10  # Log every 10 batches
checkpoint_interval = 100
output_dir = "/home/<USER>/Documents/Anas/age_verification_gemma3n/models"  # Directory to save model checkpoints
manifest_file_train = "/home/<USER>/Documents/Anas/jmsc_age_detection/dataset/annotations.json"
manifest_file_val = "/home/<USER>/Documents/Anas/jmsc_age_detection/dataset/annotations.json"
image_base_dir = "/home/<USER>/Documents/Anas/jmsc_age_detection/dataset"
age_threshold = 30  # Age threshold for classification
freeze_strategy = "last_attention_only"

# --- Setup Device ---
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")


def freeze_model_with_strategy(model, strategy="granular"):
    """
    Freeze the model with different strategies for fine-tuning.

    Args:
        model: The model to freeze
        strategy: Freezing strategy to apply. Options:
            - "none": No freezing, all parameters trainable
            - "all": Freeze all parameters except classification head
            - "last_layer_only": Freeze all except last transformer layer and classification head
            - "last_attention_only": Freeze all except last layer attention and classification head
            - "granular": Freeze all except specific components of last layer and classification head
            - "last_n_layers": Freeze all except last N transformer layers and classification head (N=2)
    """
    print(f"Applying freezing strategy: {strategy}")

    # First, freeze all parameters
    for param in model.parameters():
        param.requires_grad = False

    # Always unfreeze classification head regardless of strategy
    for name, param in model.named_parameters():
        if "classifier" in name or "score" in name:
            param.requires_grad = True
            print(f"Unfreezing classification head: {name}")

    if strategy == "none":
        # Unfreeze all parameters
        for param in model.parameters():
            param.requires_grad = True
        print("All parameters unfrozen")

    elif strategy == "all":
        # Only classification head is unfrozen (already done above)
        print("Only classification head unfrozen")

    elif strategy == "last_layer_only":
        # Unfreeze the entire last transformer layer
        num_layers = len(model.model.language_model.layers)
        last_layer_idx = num_layers - 1
        last_layer = model.model.language_model.layers[last_layer_idx]

        for param in last_layer.parameters():
            param.requires_grad = True
        print(f"Unfrozen entire last transformer layer (layer {last_layer_idx})")

        # Also unfreeze final model norm
        for name, param in model.model.language_model.named_parameters():
            if name == "norm.weight":
                param.requires_grad = True
                print(f"Unfreezing final norm: {name}")

    elif strategy == "last_attention_only":
        # Unfreeze only attention components of the last layer
        num_layers = len(model.model.language_model.layers)
        last_layer_idx = num_layers - 1
        last_layer = model.model.language_model.layers[last_layer_idx]

        if hasattr(last_layer, "self_attn"):
            for param in last_layer.self_attn.parameters():
                param.requires_grad = True
            print("Unfrozen self-attention in last layer")

        # Also unfreeze attention layer norms
        if hasattr(last_layer, "input_layernorm"):
            for param in last_layer.input_layernorm.parameters():
                param.requires_grad = True
            print("Unfrozen input layernorm in last layer")

    elif strategy == "granular":
        # Unfreeze specific components of the last layer (original behavior)
        num_layers = len(model.model.language_model.layers)
        last_layer_idx = num_layers - 1
        last_layer = model.model.language_model.layers[last_layer_idx]

        components_to_unfreeze = [
            "self_attn",  # Self-attention mechanism
            "input_layernorm",  # Layer normalization before self-attention
            "post_attention_layernorm",  # Layer normalization after self-attention
            "mlp",  # Feedforward network
        ]

        for component_name in components_to_unfreeze:
            if hasattr(last_layer, component_name):
                component = getattr(last_layer, component_name)
                for param in component.parameters():
                    param.requires_grad = True
                print(f"Unfreezing {component_name} in the last layer")

        # Unfreeze final model norm
        for name, param in model.model.language_model.named_parameters():
            if name == "norm.weight":  # Final layer norm
                param.requires_grad = True
                print(f"Unfreezing final norm: {name}")

    elif strategy == "last_n_layers":
        # Unfreeze last N layers (default N=2)
        num_layers = len(model.model.language_model.layers)
        n_layers_to_unfreeze = 2

        for i in range(max(0, num_layers - n_layers_to_unfreeze), num_layers):
            layer = model.model.language_model.layers[i]
            for param in layer.parameters():
                param.requires_grad = True
            print(f"Unfrozen transformer layer {i}")

        # Also unfreeze final model norm
        for name, param in model.model.language_model.named_parameters():
            if name == "norm.weight":
                param.requires_grad = True
                print(f"Unfreezing final norm: {name}")

    else:
        raise ValueError(f"Unknown freezing strategy: {strategy}")

    # Count and report parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Total Parameters: {total_params:,}")
    print(f"Trainable Parameters: {trainable_params:,}")
    print(f"Percentage Trainable: {100 * trainable_params / total_params:.2f}%")

    return model


# --- Load Model and Processor ---
model = load_gemma3n_model_for_classification(model_name, num_labels).to(device)
# Apply freezing strategy
model = freeze_model_with_strategy(model, strategy=freeze_strategy)
processor = load_gemma3n_processor(model_name)

# --- Load Datasets and Create Dataloaders ---
train_dataset = FaceCropDataset(manifest_file=manifest_file_train,
                                image_base_dir=image_base_dir, age_threshold=age_threshold)
val_dataset = FaceCropDataset(manifest_file=manifest_file_val,
                              image_base_dir=image_base_dir, age_threshold=age_threshold)

train_loader = create_face_dataloaders(train_dataset, processor, batch_size=batch_size, shuffle=True)
val_loader = create_face_dataloaders(val_dataset, processor, batch_size=batch_size, shuffle=False)

# --- Optimizer ---
optimizer = AdamW(model.parameters(), lr=learning_rate)
# --- Loss Function ---
loss_fn = CrossEntropyLoss()

# --- Learning Rate Scheduler ---
num_training_steps = len(train_loader) * num_epochs
scheduler = get_scheduler(
    name="linear",
    optimizer=optimizer,
    num_warmup_steps=int(0.1 * num_training_steps),
    num_training_steps=num_training_steps,
)

# --- Training and Validation Loops ---
best_f1_score = -1.0
global_step = 0

for epoch in range(num_epochs):
    # Training
    model.train()
    total_train_loss = 0

    # wrap the dataloader in TQDM
    train_progress_bar = tqdm.tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs}, leave=False") 
    for batch_idx, batch in enumerate(train_loader):
        inputs = {k: v.to(device) for k, v in batch.items() if k != "labels"}
        labels = batch["labels"].to(device)

        outputs = model(**inputs, labels=labels, return_dict=True)
        loss = outputs.loss
        logits = outputs.logits

        # scale loss for gradient accumulation
        loss = loss / gradient_accumulation_steps
        loss.backward()
        total_train_loss += loss.item() * gradient_accumulation_steps  # unscale the loss before logging

        if (batch_idx + 1) % gradient_accumulation_steps == 0:
            optimizer.step()
            scheduler.step()
            optimizer.zero_grad()  # Zero gradients after step
            global_step += 1  # Increment global step only when optimizer steps

            # checkpoint saving
            if global_step % checkpoint_interval == 0:
                checkpoint_dir = os.path.join(output_dir, f"checkpoint-{global_step}")
                os.makedirs(checkpoint_dir, exist_ok=True)
                model.save_pretrained(checkpoint_dir)
                print(f"Saved checkpoint to {checkpoint_dir} at step {global_step}")
            
        
        # use a different variable for the last logged loss
        if (batch_idx + 1) % log_interval == 0:
            # Log the unscaled loss for the current step
            current_loss = loss.item() * gradient_accumulation_steps
            train_progress_bar.set_postfix(loss=current_loss)

    avg_train_loss = total_train_loss / len(train_loader)
    print(f"Epoch {epoch+1} finished. Average Train Loss: {avg_train_loss:.4f}")

    # Validation
    model.eval()
    total_val_loss = 0
    all_preds = []
    all_labels = []
    with torch.no_grad():
        for batch_idx, batch in enumerate(val_loader):
            inputs = {k: v.to(device) for k, v in batch.items() if k != "labels"}
            labels = batch["labels"].to(device)

            outputs = model(**inputs, labels=labels, return_dict=True)
            loss = outputs.loss
            logits = outputs.logits

            total_val_loss += loss.item()

            preds = torch.argmax(logits, dim=-1).cpu().numpy()
            all_preds.extend(preds)
            all_labels.extend(labels.cpu().numpy())

    avg_val_loss = total_val_loss / len(val_loader)
    accuracy = accuracy_score(all_labels, all_preds)
    f1 = f1_score(all_labels, all_preds, average='binary')  # Assuming binary classification

    print(f"Validation Results - Epoch {epoch+1}:")
    print(f"  Average Val Loss: {avg_val_loss:.4f}")
    print(f"  Accuracy: {accuracy:.4f}")
    print(f"  F1 Score: {f1:.4f}")

    # Save best model checkpoint
    if f1 > best_f1_score:
        best_f1_score = f1
        # Create output directory if it doesn't exist
        import os
        os.makedirs(output_dir, exist_ok=True)
        model_save_path = os.path.join(output_dir, f"best_model_epoch_{epoch+1}_f1_{f1:.4f}")
        model.save_pretrained(model_save_path)
        print(f"Saved best model to {model_save_path} with F1 Score: {f1:.4f}")

print("\nTraining complete!")
