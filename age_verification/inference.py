import sys
import torch
from PIL import Image

gemma_local_path = "/home/<USER>/Documents/Anas/age_verification_gemma3n/gemma3_local/src"
if gemma_local_path not in sys.path:
    sys.path.insert(0, gemma_local_path)

from transformers import AutoProcessor
from gemma3_local.src.transformers.models.gemma3n.modeling_gemma3n import Gemma3nForSequenceClassification

MODEL_PATH = "/home/<USER>/Documents/Anas/age_verification_gemma3n/models/checkpoint-300"

IMAGE_PATH = "/home/<USER>/Documents/Anas/jmsc_age_detection/dataset/KNN_AGE/face_crops/val/images/hf_age_124057261932791261_58_0_face_0.jpg"

AGE_THRESHOLD = 30

def run_inference():
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    try:
        model = Gemma3nForSequenceClassification.from_pretrained(MODEL_PATH).to(device)
    except Exception as e:
        print(f"Error loading model: {e}")
        return
    
    print("Loading processor from base model")
    processor = AutoProcessor.from_pretrained("/home/<USER>/Documents/Anas/age_verification_gemma3n/models/pretrained_gemma3n/gemma-3n-E2B")

    model.eval()

    try:
        image = Image.open(IMAGE_PATH).convert("RGB")
    except Exception as e:
        print(f"Error loading image: {e}")
        return
    
    text = "<image_soft_token>\nIs the person in the image an adult?"
    
    print(f"\nRunning inference on {IMAGE_PATH}...")

    with torch.no_grad():
        # processor to prepare the inputs for the model
        inputs = processor(
            images=[image],
            text=text,
            return_tensors="pt",
            padding=True,
            truncation=True,
        ).to(device)

        # get the model's output
        # Access the logits by index[1] to be robust against any return type issues
        outputs = model(**inputs)
        logits = outputs[1]

        # The logits are raw scores. We apply argmax to get the predicted class index 0 or 1
        predicted_class_idx = torch.argmax(logits, dim=-1).item()

        label_map = {0: "Underage", 1: "Adult"}
        predicted_label = label_map[predicted_class_idx]

        print("\n--- Inference Results ---")
        print(f"Predicted label: {predicted_label}")
        print(f"Raw logits: {logits.cpu().numpy()}")
        print(f"Confidence: {torch.softmax(logits, dim=-1).max().item() * 100:.2f}%")
        print("------------------------")

if __name__ == "__main__":
    run_inference()