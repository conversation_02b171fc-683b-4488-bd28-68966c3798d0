# In age_verification/data/dataloader.py

import torch
from torch.utils.data import DataLoader


def create_face_dataloaders(dataset, processor, batch_size, shuffle=True):
    """
    Creates a DataLoader with the definitive collate_fn that correctly handles
    a batch of dictionaries from the dataset.
    """

    def collate_fn(batch):
        """
        Correctly unnpacks a list of dictionaries into a single batch dictionary.

        Args:
            batch (list): A list of dictionaries, where each is a sample from the Dataset.
                          e.g., [{'image': img, 'text': txt, 'label': lbl}, ...]
        
        Returns:
            dict: A single batch dictionary ready for the model.
        """
        # --- The Correct Implementation ---
        # 1. Initialize empty lists to hold the components of the batch.
        processed_samples = []
        labels = []

        # 2. Loop through each dictionary in the batch and append its components by KEY.
        for item in batch:
            processed_inputs = processor(
                # Single Image
                images=item["image"],
                # Single Text
                text=item["text"],
                return_tensors="pt",
                padding=True,
                truncation=True,
            )
            processed_samples.append(processed_inputs)
            labels.append(item["label"])
        
        # Combine all processed samples into a single batch
        batch_inputs = {}
        for key in processed_samples[0].keys():
            batch_inputs[key] = torch.cat([sample[key] for sample in processed_samples], dim=0)
        
        batch_inputs["labels"] = torch.tensor(labels, dtype=torch.long)
        
        return batch_inputs

    # --- Create and return the DataLoader ---
    return DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        collate_fn=collate_fn,
        num_workers=4,
        pin_memory=False,
    )