import json
import os
from tqdm import tqdm

# --- Configuration ---
# 1. The path to your original manifest file that needs cleaning.
original_manifest_path = "/home/<USER>/Documents/Anas/jmsc_age_detection/dataset/annotations.json"

# 2. The base directory where your images are stored. This is used to build the full path.
image_base_dir = "/home/<USER>/Documents/Anas/jmsc_age_detection/dataset"

# 3. The path where the new, cleaned manifest file will be saved.
cleaned_manifest_path = "/home/<USER>/Documents/Anas/jmsc_age_detection/dataset/annotations_cleaned.json"

# 4. **IMPORTANT**: The key in your JSON that holds the relative image path.
#    You may need to look inside your `annotations_combined.json` to confirm this.
#    Common keys are "image_path", "file_path", "image", "path", etc.
#    Based on your file structure, "image_path" is a likely guess.
path_key = "image_path"
# --- End of Configuration ---


def clean_dataset_manifest():
    """
    Reads a JSON manifest, verifies that each image file exists on disk,
    and writes a new, cleaned manifest with only the valid entries.
    """
    # --- 1. Load the Original Data ---
    if not os.path.exists(original_manifest_path):
        print(f"Error: The original manifest file was not found at:\n{original_manifest_path}")
        return

    print(f"Loading original manifest from: {original_manifest_path}")
    with open(original_manifest_path, 'r') as f:
        original_data = json.load(f)
    
    if not isinstance(original_data, list):
        print(f"Error: Expected the JSON file to contain a list of objects, but found type {type(original_data)}.")
        return

    print(f"Found {len(original_data):,} total entries in the original manifest.")

    # --- 2. Verify Each Entry ---
    cleaned_data = []
    missing_count = 0
    print("\nVerifying image paths... (This may take a while for large datasets)")

    # The tqdm library provides a nice progress bar
    for entry in tqdm(original_data, desc="Cleaning Manifest"):
        if path_key not in entry:
            print(f"\nWarning: Skipping an entry because it is missing the key '{path_key}'. Entry: {entry}")
            continue

        relative_image_path = entry[path_key]
        full_image_path = os.path.join(image_base_dir, relative_image_path)

        if os.path.exists(full_image_path):
            cleaned_data.append(entry)
        else:
            missing_count += 1
            # To see which files are missing, uncomment the line below
            # print(f"Missing file: {full_image_path}")

    # --- 3. Save the Cleaned Data ---
    print("\nVerification complete.")
    print(f"  - Found {len(cleaned_data):,} valid image entries.")
    print(f"  - Removed {missing_count:,} entries for missing files.")

    print(f"\nSaving cleaned manifest to: {cleaned_manifest_path}")
    with open(cleaned_manifest_path, 'w') as f:
        json.dump(cleaned_data, f, indent=4)

    print("\nDone! Your dataset manifest is now clean.")
    print("Please update your training script to use the new file.")


if __name__ == "__main__":
    clean_dataset_manifest()