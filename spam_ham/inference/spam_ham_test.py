import torch
from transformers import AutoTokenizer
from transformers.models.gemma3n.modeling_gemma3n import Gemma3nForSequenceClassification

model_path = "/home/<USER>/Documents/Anas/age_verification_gemma3n/finetuning/results/finetuned_gemma3n_spam"
tokenizer = AutoTokenizer.from_pretrained(model_path)
model = Gemma3nForSequenceClassification.from_pretrained(model_path)

# Define the lables
labels = ["ham", "spam"]

text = "XXXMobileMovieClub: To use your credit, click the WAP link in the next txt message or click here>> http://wap. xxxmobilemovieclub.com?n=QJKGIGHJJGCBL"
inputs = tokenizer(text, return_tensors="pt")

with torch.no_grad():
    outputs = model(**inputs, return_dict=True)

# Get the predicted class ID
logits = outputs.logits
probabilities = torch.softmax(logits, dim=-1)
confidence, predicted_class_id = torch.max(probabilities, dim=-1)

predicted_label = labels[predicted_class_id.item()]

print(f"Text: {text}")
print(f"Predicted label: {predicted_label}")
print(f"Confidence: {confidence.item()}")
